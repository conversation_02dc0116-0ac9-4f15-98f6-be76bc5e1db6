import React, { useState, useEffect } from "react";
import axios from "axios";

function MusicRecommender() {
  const [tracks, setTracks] = useState([]);
  const [currentTrack, setCurrentTrack] = useState(null);
  const [player, setPlayer] = useState(null);
  const [deviceId, setDeviceId] = useState(null);
  const [accessToken, setAccessToken] = useState(null);

  // Spotify App credentials
  const clientId = "f17c58cfc99740758373b835c2301fef"; // Replace with your Spotify Client ID
  const redirectUri = "http://127.0.0.1:5173/callback"; // Set in Spotify Dashboard

  // Mood → Search Query Mapping
  const moodQueries = {
    happy: "Bollywood party hits",
    sad: "Sad Hindi songs",
    romantic: "Romantic Bollywood",
    energetic: "Workout Hindi",
    calm: "Bollywood lofi",
  };

  // Spotify Authorization (PKCE Flow)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get("code");

    if (code && !accessToken) {
      getAccessTokenFromCode(code);
    }
  }, [accessToken]);

  const generateCodeVerifier = (length) => {
    const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    return Array(length)
      .fill()
      .map(() => possible.charAt(Math.floor(Math.random() * possible.length)))
      .join("");
  };

  const generateCodeChallenge = async (codeVerifier) => {
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const digest = await crypto.subtle.digest("SHA-256", data);
    return btoa(String.fromCharCode(...new Uint8Array(digest)))
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=+$/, "");
  };

  const loginWithSpotify = async () => {
    const codeVerifier = generateCodeVerifier(128);
    const codeChallenge = await generateCodeChallenge(codeVerifier);
    sessionStorage.setItem("code_verifier", codeVerifier);

    const scope = "streaming user-read-email user-read-private";
    const authUrl = new URL("https://accounts.spotify.com/authorize");
    authUrl.search = new URLSearchParams({
      client_id: clientId,
      response_type: "code",
      redirect_uri: redirectUri,
      code_challenge_method: "S256",
      code_challenge: codeChallenge,
      scope,
    }).toString();

    window.location.href = authUrl.toString();
  };

  const getAccessTokenFromCode = async (code) => {
    const codeVerifier = sessionStorage.getItem("code_verifier");
    try {
      const res = await axios.post(
        "https://accounts.spotify.com/api/token",
        new URLSearchParams({
          grant_type: "authorization_code",
          code,
          redirect_uri: redirectUri,
          client_id: clientId,
          code_verifier: codeVerifier,
        }),
        {
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
        }
      );
      setAccessToken(res.data.access_token);
      window.history.replaceState({}, document.title, "/"); // Clear query params
    } catch (err) {
      console.error("Token fetch error:", err);
    }
  };

  // Initialize Spotify Web Playback SDK
  useEffect(() => {
    if (!accessToken) return;

    window.onSpotifyWebPlaybackSDKReady = () => {
      const spotifyPlayer = new window.Spotify.Player({
        name: "Mood Based Music Recommender",
        getOAuthToken: (cb) => cb(accessToken),
        volume: 0.5,
      });

      spotifyPlayer.addListener("ready", ({ device_id }) => {
        setDeviceId(device_id);
        setPlayer(spotifyPlayer);
      });

      spotifyPlayer.addListener("not_ready", ({ device_id }) => {
        console.log("Device ID has gone offline", device_id);
      });

      spotifyPlayer.addListener("player_state_changed", (state) => {
        if (!state) return;
        const track = state.track_window.current_track;
        setCurrentTrack({
          id: track.id,
          title: track.name,
          artist: track.artists.map((a) => a.name).join(", "),
          image: track.album.images[0]?.url,
          uri: track.uri,
        });
      });

      spotifyPlayer.connect();
    };

    const script = document.createElement("script");
    script.src = "https://sdk.scdn.co/spotify-player.js";
    script.async = true;
    document.body.appendChild(script);

    return () => {
      if (player) player.disconnect();
    };
  }, [accessToken]);

  // Fetch songs from Spotify
  const fetchTracks = async (mood) => {
    if (!accessToken) {
      console.error("No access token available");
      return;
    }
    try {
      const query = moodQueries[mood] || "Bollywood hits";
      const res = await axios.get("https://api.spotify.com/v1/search", {
        headers: { Authorization: `Bearer ${accessToken}` },
        params: { q: query, type: "track", limit: 10 },
      });

      const items = res.data.tracks.items.map((item) => ({
        id: item.id,
        title: item.name,
        artist: item.artists.map((a) => a.name).join(", "),
        uri: item.uri, // Use track URI for playback
        image: item.album.images[0]?.url,
      }));

      setTracks(items);
      if (items[0] && deviceId) {
        setCurrentTrack(items[0]);
        playTrack(items[0].uri);
      }
    } catch (err) {
      console.error("Fetch tracks error:", err);
    }
  };

  // Play a track
  const playTrack = async (trackUri) => {
    if (!deviceId || !accessToken) return;
    try {
      await axios.put(
        `https://api.spotify.com/v1/me/player/play?device_id=${deviceId}`,
        { uris: [trackUri] },
        { headers: { Authorization: `Bearer ${accessToken}` } }
      );
    } catch (err) {
      console.error("Playback error:", err);
    }
  };

  // Pause playback
  const pauseTrack = async () => {
    if (!deviceId || !accessToken) return;
    try {
      await axios.put(
        "https://api.spotify.com/v1/me/player/pause",
        {},
        { headers: { Authorization: `Bearer ${accessToken}` } }
      );
    } catch (err) {
      console.error("Pause error:", err);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-900 via-black to-gray-900 text-white flex flex-col items-center p-6">
      <h1 className="text-3xl font-bold mb-6">🎶 Mood Based Music Recommender</h1>

      {/* Login Button */}
      {!accessToken && (
        <button
          onClick={loginWithSpotify}
          className="bg-green-600 hover:bg-green-500 px-5 py-2 rounded-full shadow-lg transition mb-8"
        >
          Login with Spotify
        </button>
      )}

      {/* Mood Buttons */}
      {accessToken && (
        <div className="flex flex-wrap gap-3 mb-8">
          {Object.keys(moodQueries).map((mood) => (
            <button
              key={mood}
              onClick={() => fetchTracks(mood)}
              className="bg-green-600 hover:bg-green-500 px-5 py-2 rounded-full shadow-lg transition"
            >
              {mood.charAt(0).toUpperCase() + mood.slice(1)}
            </button>
          ))}
        </div>
      )}

      {/* Current Playing Track */}
      {currentTrack && (
        <div className="bg-gray-800 p-6 rounded-2xl shadow-lg w-full max-w-md mb-8 text-center">
          <img
            src={currentTrack.image}
            alt={currentTrack.title}
            className="rounded-lg mx-auto mb-4 w-40"
          />
          <h2 className="text-xl font-semibold">{currentTrack.title}</h2>
          <p className="text-gray-300">{currentTrack.artist}</p>
          <div className="mt-4 flex justify-center gap-4">
            <button
              onClick={() => playTrack(currentTrack.uri)}
              className="bg-green-600 hover:bg-green-500 px-4 py-2 rounded-full"
            >
              Play
            </button>
            <button
              onClick={pauseTrack}
              className="bg-red-600 hover:bg-red-500 px-4 py-2 rounded-full"
            >
              Pause
            </button>
          </div>
        </div>
      )}

      {/* Track List */}
      <div className="grid gap-4 w-full max-w-2xl">
        {tracks.map((track) => (
          <div
            key={track.id}
            className="flex items-center gap-4 bg-gray-800 p-3 rounded-xl hover:bg-gray-700 cursor-pointer"
            onClick={() => {
              setCurrentTrack(track);
              playTrack(track.uri);
            }}
          >
            <img src={track.image} alt={track.title} className="w-14 rounded" />
            <div>
              <p className="font-medium">{track.title}</p>
              <p className="text-gray-400 text-sm">{track.artist}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default MusicRecommender;