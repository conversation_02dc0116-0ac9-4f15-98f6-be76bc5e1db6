import React, { useEffect, useState } from "react";
import { Play, Clock, Music, Heart, MoreHorizontal } from "lucide-react";

const MusicRecommender = ({mood}) => {
  console.log(mood)
  const [tracks, setTracks] = useState([]);
  const [currentTrack, setCurrentTrack] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isPlaying, setIsPlaying] = useState(false);

  // 🔑 Your NoCodeAPI endpoint for Spotify
  const API_BASE = import.meta.env.VITE_BASE_URL;


  // Using fetch instead of axios for compatibility
  const fetchTracks = async (mood) => {
    try {
      setLoading(true);
      setError("");
      setTracks([]);
      setCurrentTrack(null);

      // Search for playlists
      const res = await fetch(`${API_BASE}/search?q=${mood}&type=playlist`);
      const data = await res.json();
      const playlist = data.playlists?.items?.[0];
      
      if (!playlist) {
        setError("⚠️ No playlist found for this mood.");
        setLoading(false);
        return;
      }

      // Fetch playlist details
      const playlistRes = await fetch(`${API_BASE}/playlists?id=${playlist.id}`);
      const playlistData = await playlistRes.json();

      const fetchedTracks = playlistData.tracks.items
        .map((t) => t.track)
        .filter((t) => t && t.preview_url);

      if (fetchedTracks.length === 0) {
        setError("⚠️ No playable tracks found in this playlist.");
        setLoading(false);
        return;
      }

      setTracks(fetchedTracks);
      setCurrentTrack(fetchedTracks[0]);
      setLoading(false);
    } catch (err) {
      console.error("Error fetching tracks:", err);
      setError("❌ Failed to fetch music. Try again!");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (mood) {
      fetchTracks(mood);
    }
  }, [mood]);

  const playNextTrack = () => {
    const idx = tracks.findIndex((t) => t.id === currentTrack.id);
    const nextTrack = tracks[idx + 1];
    if (nextTrack) setCurrentTrack(nextTrack);
  };

  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const togglePlayPause = () => {
    const audio = document.querySelector('audio');
    if (audio) {
      if (isPlaying) {
        audio.pause();
      } else {
        audio.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      {/* Top Navigation Bar */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black bg-opacity-60 backdrop-blur-sm">
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
            <Music className="w-5 h-5 text-black" />
          </div>
          <h1 className="text-xl font-bold">Mood Music Player</h1>
        </div>
        <div className="flex items-center space-x-4">
          <button className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors">
            <Heart className="w-4 h-4" />
          </button>
          <button className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors">
            <MoreHorizontal className="w-4 h-4" />
          </button>
        </div>
      </nav>

      {/* Main Content */}
      <div className="px-6 pb-32">
        {/* Hero Section with Mood Selection */}
        <div className="mb-8">


          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-green-500 border-t-transparent"></div>
              <p className="ml-4 text-xl text-gray-300">Loading your music...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-900 bg-opacity-50 border border-red-700 rounded-lg p-4 mb-6">
              <p className="text-red-200">{error}</p>
            </div>
          )}
        </div>

        {/* Current Playing Track */}
        {currentTrack && (
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl p-6 mb-8 shadow-2xl">
            <div className="flex items-center space-x-6">
              <div className="relative flex-shrink-0">
                <img
                  src={currentTrack.album.images[0]?.url}
                  alt="album cover"
                  className="w-20 h-20 rounded-lg shadow-lg"
                />
                <button
                  onClick={togglePlayPause}
                  className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center hover:bg-green-400 transition-colors shadow-lg"
                >
                  <Play className="w-4 h-4 text-black ml-0.5" />
                </button>
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-xl font-bold text-white truncate mb-1">
                  {currentTrack.name}
                </h3>
                <p className="text-gray-300 truncate mb-1">
                  {currentTrack.artists.map((a) => a.name).join(", ")}
                </p>
                <p className="text-sm text-gray-400 truncate">
                  {currentTrack.album.name}
                </p>
              </div>
            </div>

            {/* Audio Player */}
            <div className="mt-6">
              <audio
                src={currentTrack.preview_url}
                controls
                autoPlay
                onEnded={playNextTrack}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                className="w-full h-12 bg-gray-700 rounded-lg"
                controlsList="nodownload"
              />
            </div>
          </div>
        )}

        {/* Playlist Table */}
        {tracks.length > 0 && (
          <div className="bg-gray-900 bg-opacity-50 rounded-xl overflow-hidden">
            <div className="p-6 border-b border-gray-800">
              <h3 className="text-2xl font-bold text-white mb-2">Your Playlist</h3>
              <p className="text-gray-400">{tracks.length} songs</p>
            </div>

            {/* Table Header */}
            <div className="grid grid-cols-12 gap-4 px-6 py-3 text-xs font-medium text-gray-400 uppercase tracking-wider border-b border-gray-800 bg-gray-900 bg-opacity-30">
              <div className="col-span-1 flex items-center justify-center">#</div>
              <div className="col-span-6">Title</div>
              <div className="col-span-4">Album</div>
              <div className="col-span-1 flex items-center justify-end">
                <Clock className="w-4 h-4" />
              </div>
            </div>

            {/* Song Rows */}
            <div className="max-h-96 overflow-y-auto">
              {tracks.map((track, index) => {
                const isCurrentTrack = currentTrack?.id === track.id;
                return (
                  <div
                    key={track.id}
                    onClick={() => setCurrentTrack(track)}
                    className={`grid grid-cols-12 gap-4 px-6 py-3 cursor-pointer transition-colors duration-150 group hover:bg-gray-800 hover:bg-opacity-50 ${
                      isCurrentTrack 
                        ? "bg-green-900 bg-opacity-30 text-green-400" 
                        : "text-gray-300 hover:text-white"
                    }`}
                  >
                    {/* Track Number */}
                    <div className="col-span-1 flex items-center justify-center">
                      {isCurrentTrack ? (
                        <div className="w-4 h-4 flex items-center justify-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                      ) : (
                        <>
                          <span className="text-sm text-gray-400 group-hover:hidden">
                            {index + 1}
                          </span>
                          <Play className="w-4 h-4 hidden group-hover:block text-white" />
                        </>
                      )}
                    </div>

                    {/* Track Info */}
                    <div className="col-span-6 flex items-center space-x-3 min-w-0">
                      <img
                        src={track.album.images[2]?.url || track.album.images[0]?.url}
                        alt="album"
                        className="w-10 h-10 rounded flex-shrink-0"
                      />
                      <div className="min-w-0">
                        <p className={`font-medium truncate ${
                          isCurrentTrack ? "text-green-400" : "text-white"
                        }`}>
                          {track.name}
                        </p>
                        <p className="text-sm text-gray-400 truncate">
                          {track.artists.map((a) => a.name).join(", ")}
                        </p>
                      </div>
                    </div>

                    {/* Album */}
                    <div className="col-span-4 flex items-center min-w-0">
                      <p className="text-sm text-gray-400 truncate hover:text-white transition-colors">
                        {track.album.name}
                      </p>
                    </div>

                    {/* Duration */}
                    <div className="col-span-1 flex items-center justify-end">
                      <span className="text-sm text-gray-400">
                        {track.duration_ms ? formatDuration(track.duration_ms) : "0:30"}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Bottom Player Bar (if track is playing) */}
      {currentTrack && (
        <div className="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 px-6 py-4 z-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1 min-w-0">
              <img
                src={currentTrack.album.images[2]?.url || currentTrack.album.images[0]?.url}
                alt="album"
                className="w-12 h-12 rounded"
              />
              <div className="min-w-0">
                <p className="text-white font-medium truncate">{currentTrack.name}</p>
                <p className="text-gray-400 text-sm truncate">
                  {currentTrack.artists.map((a) => a.name).join(", ")}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="text-gray-400 hover:text-white transition-colors">
                <Heart className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MusicRecommender;