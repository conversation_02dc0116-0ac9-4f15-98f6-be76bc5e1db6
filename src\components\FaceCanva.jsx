import { useEffect, useRef, useState } from "react";
import * as faceapi from "face-api.js";
import { Loader2 } from "lucide-react";
import MusicRecommender from "./MusicRecommender";

const FaceDetector = () => {
  const videoRef = useRef(null); // Reference to video element
  const canvasRef = useRef(null); // Reference to canvas overlay

  const [isLoading, setIsLoading] = useState(true); // Loader state
  const [error, setError] = useState(null); // Error state
  const [modelLoadingProgress, setModelLoadingProgress] = useState(0); // Model loading % tracker
  const [mood, setMood] = useState(null);
  const [moodValue, setMoodvalue] = useState(null);

  console.log(mood)
  // 🔹 Load face-api models on mount
  useEffect(() => {
    const loadModels = async () => {
      try {
        setIsLoading(true);
        const MODEL_URL = "/models"; // Models must be in public/models folder

        setModelLoadingProgress(25);
        await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);

        setModelLoadingProgress(50);
        await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);

        setModelLoadingProgress(75);
        await faceapi.nets.faceExpressionNet.loadFromUri(MODEL_URL);

        setModelLoadingProgress(100);
        await faceapi.nets.ageGenderNet.loadFromUri(MODEL_URL);

        setIsLoading(false);
        startVideo(); // Start camera after models load
      } catch (err) {
        console.error(err);
        setError("Failed to load face detection models");
        setIsLoading(false);
      }
    };
    loadModels();
  }, []);

  // 🔹 Start webcam
  const startVideo = () => {
    navigator.mediaDevices
      .getUserMedia({ video: { width: 640, height: 480 } })
      .then((stream) => {
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      })
      .catch((err) => {
        setError("Camera access denied or not available");
        console.error("Error: ", err);
      });
  };

  // 🔹 Detect faces continuously when video starts
  useEffect(() => {
    if (!videoRef.current) return;

    const handlePlay = () => {
      if (canvasRef.current) {
        canvasRef.current.innerHTML = "";
      }

      // Create canvas overlay
      const canvas = faceapi.createCanvasFromMedia(videoRef.current);
      if (canvasRef.current) {
        canvasRef.current.appendChild(canvas);
      }

      const displaySize = {
        width: videoRef.current.videoWidth || 640,
        height: videoRef.current.videoHeight || 480,
      };
      faceapi.matchDimensions(canvas, displaySize);

      // Position overlay above video
      canvas.style.position = "absolute";
      canvas.style.top = "0";
      canvas.style.left = "0";
      canvas.style.zIndex = "10";

      // Detection function
      const detectFaces = async () => {
        if (!videoRef.current) return;

        try {
          const detections = await faceapi
            .detectAllFaces(
              videoRef.current,
              new faceapi.TinyFaceDetectorOptions()
            )
            .withFaceLandmarks()
            .withFaceExpressions()
            .withAgeAndGender();

          const ctx = canvas.getContext("2d");
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const resized = faceapi.resizeResults(detections, displaySize);

            faceapi.draw.drawDetections(canvas, resized);
            faceapi.draw.drawFaceLandmarks(canvas, resized);

            // 🔹 Find and log dominant expression
            resized.forEach((detection) => {
              const { age, gender, genderProbability, expressions } = detection;
              const box = detection.detection.box;

              let expressionName = "unknown";
              let expressionValue = 0;

              // ✅ Safely calculate max expression only if expressions exist
              if (expressions && Object.keys(expressions).length > 0) {
                const maxExpression = Object.entries(expressions).reduce(
                  (prev, curr) => (curr[1] > prev[1] ? curr : prev)
                );
                if (maxExpression) {
                  [expressionName, expressionValue] = maxExpression;
                }
              }

              // Update state safely
              setMood(expressionName);
              setMoodvalue(expressionValue);

              // Label with Age, Gender, Expression
              const text = `${Math.round(age)}y, ${gender} (${(
                genderProbability * 100
              ).toFixed(0)}%) | ${expressionName}`;

              const drawBox = new faceapi.draw.DrawBox(box, {
                label: text,
                boxColor: "#15803d",
                lineWidth: 3,
              });
              drawBox.draw(canvas);
            });

          }
        } catch (err) {
          console.error("Detection error:", err);
        }
      };

      // Run detection every 200ms
      const interval = setInterval(detectFaces, 200);
      return () => clearInterval(interval);
    };

    videoRef.current.addEventListener("play", handlePlay);

    return () => {
      if (videoRef.current) {
        videoRef.current.removeEventListener("play", handlePlay);
      }
    };
  }, []);

  // 🔹 Error UI
  if (error) {
    return (
      <div className="w-full h-screen flex items-center justify-center text-red-600 font-semibold">
        {error}
      </div>
    );
  }

  return (
    <>
      <div className="w-full h-screen flex items-center justify-center bg-gray-900">
        <div className="relative inline-block rounded-xl overflow-hidden shadow-2xl bg-black">
          {/* Loader */}
          {isLoading && (
            <div className="absolute inset-0 flex flex-col items-center justify-center space-y-4 bg-black/90 text-white">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
              <p className="text-lg font-medium">Loading AI Models...</p>
              <div className="w-64 bg-gray-700 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${modelLoadingProgress}%` }}
                />
              </div>
              <p className="text-sm">{modelLoadingProgress}%</p>
            </div>
          )}

          {/* Video */}
          <video
            ref={videoRef}
            autoPlay
            muted
            width="640"
            height="480"
            className="block"
            style={{ maxWidth: "100%", height: "auto" }}
          />

          {/* Canvas Overlay */}
          <div
            ref={canvasRef}
            className="absolute top-0 left-0 w-full h-full pointer-events-none"
          />
        </div>
      </div>
      <MusicRecommender mood={mood}/>
    </>
  );
};

export default FaceDetector;
